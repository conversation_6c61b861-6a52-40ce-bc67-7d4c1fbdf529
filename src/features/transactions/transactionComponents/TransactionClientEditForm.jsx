import { Formik, Form } from "formik";
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import {
  <PERSON><PERSON>,
  Confirm,
  Divider,
  Grid,
  Header,
  Icon,
  Segment,
  Popup,
} from "semantic-ui-react";
import * as Yup from "yup";
import { toast } from "react-toastify";
import {
  deletePartyInDb,
  updateTransactionInDb,
  updateTransClientInDb,
} from "../../../app/firestore/firestoreService";
import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import MySelectInput from "../../../app/common/form/MySelectInput";
import {
  createClientRoleOptions,
  createRestrictedEmails,
  partyIsBuyerOrSeller,
  partyIsSeller,
} from "../../../app/common/util/util";
import FormParty from "../../../app/common/form/FormParty";
import FormAddress from "../../../app/common/form/FormAddress";
import MyCheckbox from "../../../app/common/form/MyCheckbox";
import MyTextInput from "../../../app/common/form/MyTextInput";
import { useMediaQuery } from "react-responsive";

export default function TransactionClientEditForm({ client, transaction }) {
  const dispatch = useDispatch();
  const { allParties } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const { docsTransActive, docsTransArchived, docsTransShared } = useSelector(
    (state) => state.doc
  );
  const { tasksTransWithDateAll } = useSelector((state) => state.task);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });

  const clientRoleOptions = createClientRoleOptions(client, transaction);

  const originalClientEmail = client.email;

  const restrictedEmails = createRestrictedEmails(
    currentUserProfile,
    transaction,
    allParties,
    client
  );

  let emailIsNotEditable = true;
  if (!client.email || !transaction.sharingWith?.includes(client.email)) {
    emailIsNotEditable = false;
  }

  let initialValues = {
    ...client,
    hasPoa: client.hasPoa || false,
    poaFullName: client.poaFullName || "",
    poaTitle: client.poaTitle || "",
    poaDisplayOption: client.poaDisplayOption || "client_by_poa",
    poaCustomDisplay: client.poaCustomDisplay || "",
  };

  const [isTrustField, setIsTrustField] = useState(
    initialValues.isTrust ? true : false
  );
  const [hasPoaField, setHasPoaField] = useState(
    initialValues.hasPoa ? true : false
  );
  const [selectedRole, setSelectedRole] = useState(initialValues.role);

  // Watcher component for role changes
  function RoleWatcher({ role }) {
    React.useEffect(() => {
      setSelectedRole(role);
    }, [role]);
    return null;
  }

  const validationSchema = Yup.object({
    lastName: Yup.string().required("You must provide a last name"),
    role: Yup.string().required("You must provide a role"),
    email: Yup.string()
      .email("Must be a valid email")
      .notOneOf(restrictedEmails, "Can't use same email for different parties"),
    poaFullName: Yup.string().when("hasPoa", {
      is: true,
      then: (schema) => schema.required("POA full name is required"),
    }),
    poaTitle: Yup.string().when("hasPoa", {
      is: true,
      then: (schema) => schema.required("POA title is required"),
    }),
    poaCustomDisplay: Yup.string().when(["hasPoa", "poaDisplayOption"], {
      is: (hasPoa, poaDisplayOption) => hasPoa && poaDisplayOption === "custom",
      then: (schema) => schema.required("Custom display text is required"),
    }),
  });

  const poaDisplayOptions = [
    {
      key: "client_by_poa",
      value: "client_by_poa",
      text: "Client's name, by POA's name, as Attorney-in-Fact",
    },
    {
      key: "poa_for_client",
      value: "poa_for_client",
      text: "POA's name, as Attorney-in-Fact for Client's name",
    },
    {
      key: "custom",
      value: "custom",
      text: "Custom display (enter below)",
    },
  ];

  async function handleDelete() {
    try {
      if (client.role === "Buyer 2" || client.role === "Seller 2") {
        deletePartyInDb(client);
        if (transaction.clientThird?.lastName?.length > 0) {
          deletePartyInDb(transaction.clientThird);
        }
        updateTransactionInDb(transaction.id, {
          clientSecondary: {},
          clientSecondaryExists: false,
          // to keep it simple, delete third client as well if second deleted
          clientThird: {},
          clientThirdExists: false,
        });
      } else if (client.role === "Buyer 3" || client.role === "Seller 3") {
        deletePartyInDb(client);
        updateTransactionInDb(transaction.id, {
          clientThird: {},
          clientThirdExists: false,
        });
      }

      toast.success("client successfully deleted");
      dispatch(
        closeModal({
          modalType: "TransactionClientEditForm",
        })
      );
    } catch (error) {
      toast.error(error.message);
    }
  }

  return (
    <ModalWrapper>
      <Segment clearing>
        <div className="medium horizontal margin small top margin">
          <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                const docsInTransaction =
                  values.email !== originalClientEmail
                    ? docsTransActive?.docs
                        ?.concat(docsTransArchived?.docs)
                        .concat(docsTransShared?.docs)
                    : [];

                const docsNeedSharingUpdated = docsInTransaction
                  ? docsInTransaction?.filter((doc) =>
                      doc.sharingWith?.includes(originalClientEmail)
                    )
                  : [];
                await updateTransClientInDb(
                  transaction,
                  values,
                  originalClientEmail,
                  docsNeedSharingUpdated,
                  tasksTransWithDateAll
                );

                setSubmitting(false);
                toast.success("Client successfully updated");
                dispatch(
                  closeModal({
                    modalType: "TransactionClientEditForm",
                  })
                );
              } catch (error) {
                toast.error(error.message);
                setSubmitting(false);
              }
            }}
          >
            {({ isSubmitting, dirty, isValid, values }) => (
              <Form className="ui form">
                <RoleWatcher role={values.role} />
                <Header size="large" color="blue">
                  Edit Client
                </Header>
                <Divider />
                <Grid>
                  <Grid.Column mobile={16} computer={5}>
                    <MySelectInput
                      name="role"
                      placeholder="Role"
                      options={clientRoleOptions}
                    />
                  </Grid.Column>
                </Grid>
                <Divider />
                <FormParty
                  edit={emailIsNotEditable}
                  role={values.role}
                  newPerson={false}
                />

                {partyIsBuyerOrSeller(client.role) && (
                  <>
                    <br />
                    <MyCheckbox
                      name="isTrust"
                      label="This client is acting on behalf of a company, trust, or other entity"
                      onClick={() => setIsTrustField(!isTrustField)}
                    />
                    {isTrustField && (
                      <MyTextInput
                        name="entityName"
                        label="Company, trust, or entity name"
                      />
                    )}
                  </>
                )}

                {/* POA Section - Only show for Seller roles */}
                {partyIsSeller(selectedRole) && (
                  <>
                    <br />
                    <MyCheckbox
                      name="hasPoa"
                      label="This client will be represented by a Power of Attorney (POA)"
                      onClick={() => setHasPoaField(!hasPoaField)}
                    />
                    {hasPoaField && (
                      <>
                        <Grid>
                          <Grid.Row>
                            <Grid.Column mobile={16} computer={8}>
                              <MyTextInput
                                name="poaFullName"
                                label="POA's Full Name"
                                placeholder="Enter POA's full name"
                              />
                            </Grid.Column>
                            <Grid.Column mobile={16} computer={8}>
                              <MyTextInput
                                name="poaTitle"
                                label="POA's Title"
                                placeholder="Enter POA's title"
                              />
                            </Grid.Column>
                          </Grid.Row>
                          <Grid.Row>
                            <Grid.Column width={16}>
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  marginBottom: "8px",
                                }}
                              >
                                <label style={{ marginRight: "8px" }}>
                                  Signature Display Option
                                </label>
                                <Popup
                                  trigger={
                                    <Icon name="info circle" color="blue" />
                                  }
                                  content={
                                    <div>
                                      <p>
                                        <strong>
                                          Examples of how signatures will
                                          display:
                                        </strong>
                                      </p>
                                      <p>
                                        <strong>Option A:</strong> John Q.
                                        Principal, by Jane R. Agent, as
                                        Attorney-in-Fact
                                      </p>
                                      <p>
                                        <strong>Option B:</strong> Jane R.
                                        Agent, as Attorney-in-Fact for John Q.
                                        Principal
                                      </p>
                                      <p>
                                        <strong>Custom:</strong> You can enter
                                        your own display format
                                      </p>
                                      <br />
                                      <p>
                                        <strong>Note:</strong> The email for
                                        this client should be the POA's email
                                        address.
                                      </p>
                                      <p>
                                        <strong>
                                          Initials will display as:
                                        </strong>{" "}
                                        Client's initials by POA's initials
                                        (AIF)
                                      </p>
                                      <p>
                                        <strong>Example:</strong> JQP by JRA
                                        (AIF)
                                      </p>
                                    </div>
                                  }
                                  wide="very"
                                  position="top center"
                                />
                              </div>
                              <MySelectInput
                                name="poaDisplayOption"
                                options={poaDisplayOptions}
                                placeholder="Select display option"
                              />
                            </Grid.Column>
                          </Grid.Row>
                          {values.poaDisplayOption === "custom" && (
                            <Grid.Row>
                              <Grid.Column width={16}>
                                <MyTextInput
                                  name="poaCustomDisplay"
                                  label="Custom Signature Display"
                                  placeholder="Enter how you want the signature to display"
                                />
                              </Grid.Column>
                            </Grid.Row>
                          )}
                        </Grid>
                      </>
                    )}
                  </>
                )}

                <Divider />
                <FormAddress />
                <Divider className="medium top margin" />
                <Button
                  loading={isSubmitting}
                  disabled={!isValid || !dirty || isSubmitting}
                  type="submit"
                  floated={isMobile ? null : "right"}
                  primary
                  content="Submit"
                  className={isMobile ? "fluid medium bottom margin" : null}
                />
                <Button
                  disabled={isSubmitting}
                  onClick={() =>
                    dispatch(
                      closeModal({
                        modalType: "TransactionClientEditForm",
                      })
                    )
                  }
                  to="#"
                  type="button"
                  floated={isMobile ? null : "right"}
                  content="Cancel"
                  className={isMobile ? "fluid medium bottom margin" : null}
                />
                {!(client.role === "Buyer" || client.role === "Seller") &&
                  !isMobile && (
                    <Icon
                      name="trash"
                      link
                      onClick={() => setConfirmOpen(true)}
                    />
                  )}
                {!(client.role === "Buyer" || client.role === "Seller") &&
                  isMobile && (
                    <Button
                      type="button"
                      basic
                      color="red"
                      content="Delete"
                      fluid
                      onClick={() => setConfirmOpen(true)}
                    />
                  )}
              </Form>
            )}
          </Formik>
        </div>
      </Segment>
      <Confirm
        open={confirmOpen}
        onCancel={() => setConfirmOpen(false)}
        onConfirm={() => handleDelete()}
      ></Confirm>
    </ModalWrapper>
  );
}
