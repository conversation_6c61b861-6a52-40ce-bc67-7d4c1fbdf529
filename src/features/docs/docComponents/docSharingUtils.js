import { openModal } from "../../../app/common/modals/modalSlice";
import { 
  addHistoryToDb,
  sendDocSharingEmail,
  updateDocAddSharingInDb,
  updateTransAddSharingInDb, 
  // sendOfferSubmittedEmail, 
} from "../../../app/firestore/firestoreService";
import { convertRoleToSharingDisplay, partyIsAgent } from "../../../app/common/util/util";

/**
 * Shared function to handle document email sending with custom email check
 * This function checks if the document has canCustomizeEmailOnShare field and shows
 * the custom email modal if applicable, otherwise sends the standard email
 */
export function handleDocumentEmailSending(party, doc, transaction, currentUserProfile, dispatch) {
  const agentProfile = transaction.agentProfile?.lastName
    ? transaction.agentProfile
    : currentUserProfile;
  
  // Check if document has canCustomizeEmailOnShare field and if the party role is in the array
  const roleDisplayName = convertRoleToSharingDisplay(party.role);
  
  if (
    // currentUserProfile.isBetaTester &&
    !currentUserProfile.isTester &&
    doc.canCustomizeEmailOnShare &&
    Array.isArray(doc.canCustomizeEmailOnShare) &&
    doc.canCustomizeEmailOnShare.includes(roleDisplayName)
  ) {
    // Show custom email modal (bypasses 10-minute email throttle)
    dispatch(
      openModal({
        modalType: "DocCustomEmailModal",
        modalProps: { doc: doc, party: party, transaction: transaction },
      })
    );
  } else {
    // Send standard email
    sendDocSharingEmail([party], "sharing", agentProfile, transaction);
  }
}

/** Shared function to handle updating on share */
export async function handleDocSharingUpdate(doc, party, allParties, transaction, currentUserProfile, dispatch) {
  try {
    // Add sharing to document
    await updateDocAddSharingInDb(doc.id, party, transaction, allParties);

    // Add sharing to transaction
    await updateTransAddSharingInDb(transaction.id, party, transaction);

    // Add history entry   
    await addHistoryToDb(
      transaction.id,
      currentUserProfile,
      "shared",
      doc.name,
      party
    );
  } catch (error) {
    throw error;
  }
}

/**
 * Helper function to send offer alert email to admin if applicable
 */
export function sendOfferAlertEmailToAdmin(party, doc, currentUserProfile, transaction) {
  if (
    partyIsAgent(party.role) &&
    doc.isOffer &&   
    transaction.agentProfile?.managerDetails
      ?.sendEmailNotificationsOfOffersSentToOtherAgent
  ) {
    // sendOfferSubmittedEmail(currentUserProfile, party, transaction, doc);
  }
}

export function sendOfferAlertEmailToBrokerageStaff(party, doc, currentUserProfile, transaction) {
  // if (
  //   partyIsAgent(party.role) &&
  //   doc.isOffer &&   
  //   transaction.agentProfile?.managerDetails
  //     ?.sendEmailNotificationsOfOffersSentToOtherAgent
  // ) {
  //   sendOfferSubmittedEmail(currentUserProfile, party, transaction, doc);
  // }
}