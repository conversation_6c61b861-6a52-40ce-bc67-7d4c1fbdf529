import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Mo<PERSON>,
  Form,
  TextArea,
  Input,
  Grid,
  Segment,
} from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { closeModal } from "../../../app/common/modals/modalSlice";
import {
  updateDocAddSharingInDb,
  updateTransAddSharingInDb,
  addHistoryToDb,
  updateDocInDb,
  updatePartyInDb,
  addEmailToDb,
} from "../../../app/firestore/firestoreService";
import { functionShareWithAgent } from "../../../app/firestore/functionsService";
import { sendCustomDocSharingEmail } from "../../../app/firestore/firestoreService";
import { convertFullName, partyIsAgent } from "../../../app/common/util/util";
import { serverTimestamp } from "firebase/firestore";
import { toast } from "react-toastify";
// import _ from "lodash";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { useMediaQuery } from "react-responsive";
// import LoadingComponent from "../../../app/layout/LoadingComponent";

export default function DocCustomEmailModal({ doc, party, transaction }) {
  const dispatch = useDispatch();
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const { allParties } = useSelector((state) => state.transaction);
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [error, setError] = useState("");

  const [customSubject, setCustomSubject] = useState(
    doc?.emailSharingDefaults?.subjectPlusAddress
      ? doc.emailSharingDefaults.subjectPlusAddress +
          (transaction?.address?.street
            ? " for " + transaction?.address?.street
            : ` from ${
                transaction.agentProfile?.firstName ||
                currentUserProfile?.firstName ||
                ""
              }`)
      : `${
          transaction.agentProfile?.lastName ||
          currentUserProfile?.lastName ||
          ""
        } is sharing new documents with you.`
  );
  const [customMessage, setCustomMessage] = useState(
    (doc?.emailSharingDefaults?.message
      ? `Hi ${party.firstName || "there"}
        
        ` + doc.emailSharingDefaults.message
      : `Hi ${party.firstName || "there"},
      
I'm sharing new documents with you that you can view by clicking the "View Documents" button above. Please let me know if you have any questions.`) +
      `

Thank you,
${currentUserProfile?.firstName} ${currentUserProfile?.lastName}
${currentUserProfile?.email}`
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const agentProfile = transaction.agentProfile?.lastName
    ? transaction.agentProfile
    : currentUserProfile;

  function handleCancel() {
    dispatch(closeModal({ modalType: "DocCustomEmailModal" }));
  }

  function sendOfferAlertEmailToAdmin(party, doc) {
    if (
      partyIsAgent(party.role) &&
      doc.title === "Contract to Buy and Sell, Residential" // should be startsWith
    ) {
      // This would need to be imported if needed
      // sendOfferSubmittedEmail(currentUserProfile, party, transaction, doc);
    }
  }

  async function handleSendCustomEmail() {
    setIsSubmitting(true);

    try {
      // const partyRoleCamel = _.camelCase(party.role);
      // sharedBy is for linked agent who the agent is sharing on behalf of
      const sharedByName = agentProfile.firstName + " " + agentProfile.lastName;

      // Check if sharing with a non-user agent and handle linking
      if (
        partyIsAgent(party.role) &&
        party.isUser &&
        !party.isLinked &&
        !party.declinedLinking
      ) {
        // This case should be handled by the DocShareAgentLinking modal first
        toast.error("Agent linking should be handled first");
        setIsSubmitting(false);
        return;
      }

      // Add sharing to document
      const restoreAgentClients = false;
      await updateDocAddSharingInDb(
        doc.id,
        party,
        transaction,
        allParties,
        restoreAgentClients
      );
      await updateTransAddSharingInDb(transaction.id, party, transaction);

      // Handle linked agent case
      if (partyIsAgent(party.role) && party.isUser && party.isLinked) {
        await updateDocInDb(
          doc.id,
          {
            sentToAgent: true,
            sentToAgentId: party.id,
            sentDocIsDirty: false,
            sentToAgentAt: serverTimestamp(),
          },
          true
        );
        await functionShareWithAgent({
          doc: doc,
          party: party,
          sharedBy: sharedByName,
        });
      }

      // Send custom email
      await sendCustomDocSharingEmail(
        [party],
        "sharing",
        currentUserProfile,
        transaction,
        customSubject,
        customMessage
      );

      // Send offer alert to admin if applicable
      sendOfferAlertEmailToAdmin(party, doc);

      // Add to history
      await addHistoryToDb(
        transaction.id,
        currentUserProfile,
        "shared",
        doc.name,
        party
      );

      // Add custom email to email history
      const emailHistoryData = {
        transactionId: transaction.id,
        subject: customSubject,
        message: customMessage,
        from:
          (currentUserProfile?.firstName + " " || "") +
          (currentUserProfile?.lastName || ""),
        toEmails: [party.email],
        ccEmails:
          party.hasAssistant && party.assistant?.email
            ? [party.assistant.email]
            : [],
        ccEmailsAndRoles: [],
        bccEmails: [],
        bccEmailsAndRoles: [],
        otherEmailsBcc: "",
        otherEmailsCc: "",
        otherEmailsTo: "",
        toEmailsAndRoles: [
          {
            role: party.role,
            email: party.email,
            firstName: party.firstName,
            lastName: party.lastName,
          },
        ],
        type: "custom_document_sharing",
        documentName: doc.name,
        recipientRole: party.role,
        recipientName: convertFullName(party),
        isCustomEmail: true,
      };

      await addEmailToDb(emailHistoryData, {});

      // Update party's last email sent time
      await updatePartyInDb(party, {
        lastEmailSentAt: new Date(),
      });

      toast.success("Document shared with custom email successfully!");
      dispatch(closeModal({ modalType: "DocCustomEmailModal" }));
    } catch (error) {
      console.error("Error sharing document with custom email:", error);
      toast.error("Failed to share document. Please try again.");
      setIsSubmitting(false);
    }
  }

  return (
    <ModalWrapper open={true} size="small">
      <Segment clearing>
        <div
          className={
            isMobile
              ? "zero horizontal margin small top margin"
              : "medium horizontal margin small top margin"
          }
        >
          {/* <Modal open={true} size="small"> */}
          <Header
            icon="mail"
            content={`Customize Email to ${party.role} ${convertFullName(
              party
            )}`}
          />
          <Modal.Content>
            <p>
              <strong>To:</strong> {party.role} {convertFullName(party)}
              <br />
              <strong>Document:</strong> {doc.name}
            </p>

            <Form>
              <Grid>
                <Grid.Row>
                  <Grid.Column width={16}>
                    <label>
                      <strong>Subject Line:</strong>
                    </label>
                    <Input
                      fluid
                      value={customSubject}
                      onChange={(e) => setCustomSubject(e.target.value)}
                      placeholder="Email subject"
                    />
                  </Grid.Column>
                </Grid.Row>

                <Grid.Row>
                  <Grid.Column width={16}>
                    <label>
                      <strong>Message:</strong>
                    </label>
                    <TextArea
                      value={customMessage}
                      onChange={(e) => {
                        let value = e.target.value;

                        const allowed = /^[a-zA-Z0-9\s'!.,()$%#@&:?]*$/m;

                        if (!allowed.test(value)) {
                          setError(
                            "Only letters, numbers, spaces, and ' ! . , ( ) $ % # @ & : ? are allowed."
                          );
                          // Strip invalid characters, but keep newlines
                          value = value.replace(
                            /[^a-zA-Z0-9\s'!.,()$%#@&:?]/g,
                            ""
                          );
                        } else {
                          setError("");
                        }

                        //  Enforce 1,000-character limit
                        if (value.length > 2000) {
                          value = value.slice(0, 2000);
                          setError("Message cannot exceed 2000 characters.");
                        }

                        setCustomMessage(value);
                      }}
                      placeholder="Email message"
                      rows={11}
                      style={{ width: "100%" }}
                    />
                    <div
                      style={{
                        textAlign: "right",
                        fontSize: "0.85em",
                        color: "#666",
                      }}
                    >
                      {customMessage.length}/2000
                    </div>
                    {error && (
                      <div
                        style={{
                          color: "red",
                          fontSize: "0.85em",
                          marginTop: "4px",
                        }}
                      >
                        {error}
                      </div>
                    )}
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Form>

            {/* <div
              style={{
                backgroundColor: "#f8f9fa",
                padding: "15px",
                borderRadius: "5px",
                marginTop: "15px",
              }}
            >
              <p>
                <strong>Partial Preview of email that will be sent:</strong>
              </p>
              <p>
                <strong>Subject:</strong> {customSubject}
              </p>
              <p>
                <strong>Message:</strong>
              </p>
              <div
                style={{
                  fontStyle: "italic",
                  marginLeft: "10px",
                  overflowY: "auto",
                  maxHeight: "200px",
                }}
              >
                <p>Hi {party.firstName || "there"},</p>
                <p style={{ whiteSpace: "pre-wrap" }}>{customMessage}</p>
                <p>
                  Thank you,
                  <br />
                  {agentProfile.firstName} {agentProfile.lastName}
                  <br />
                  {agentProfile.email}
                </p>
              </div>
            </div> */}
          </Modal.Content>

          <Modal.Actions>
            <Button onClick={handleCancel} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              color="blue"
              onClick={handleSendCustomEmail}
              loading={isSubmitting}
              disabled={
                isSubmitting || !customSubject.trim() || !customMessage.trim()
              }
            >
              Send Email
            </Button>
          </Modal.Actions>
          {/* </Modal> */}
        </div>
      </Segment>
    </ModalWrapper>
  );
}
